"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/graphql/route";
exports.ids = ["app/api/graphql/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_ankkorwoo_ankkor_src_app_api_graphql_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/graphql/route.ts */ \"(rsc)/./src/app/api/graphql/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/graphql/route\",\n        pathname: \"/api/graphql\",\n        filename: \"route\",\n        bundlePath: \"app/api/graphql/route\"\n    },\n    resolvedPagePath: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\api\\\\graphql\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_ankkorwoo_ankkor_src_app_api_graphql_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/graphql/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/graphql/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/graphql/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n/**\n * GraphQL Proxy to handle CORS issues when connecting to WooCommerce\n * This endpoint forwards GraphQL requests to the WordPress site and handles CORS\n */ async function POST(request) {\n    try {\n        // Get the GraphQL query from the request body\n        const body = await request.json();\n        // WordPress/WooCommerce GraphQL endpoint from env variables\n        const graphqlEndpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://maroon-lapwing-781450.hostingersite.com/graphql\";\n        console.log(\"\\uD83D\\uDD17 GraphQL Proxy - Using endpoint:\", graphqlEndpoint);\n        console.log(\"\\uD83D\\uDCCA GraphQL Proxy - Request body:\", JSON.stringify(body, null, 2));\n        // Get the origin for CORS\n        const origin = request.headers.get(\"origin\") || \"\";\n        // Prepare headers for the request to WooCommerce\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            \"Accept\": \"application/json\",\n            \"User-Agent\": \"NextJS-GraphQL-Proxy/1.0\"\n        };\n        // Forward session token if present in the request\n        const sessionHeader = request.headers.get(\"woocommerce-session\");\n        if (sessionHeader) {\n            headers[\"woocommerce-session\"] = sessionHeader;\n        }\n        // Forward cookies if present\n        const cookie = request.headers.get(\"cookie\");\n        if (cookie) {\n            headers[\"cookie\"] = cookie;\n        }\n        // Forward the request to WordPress GraphQL\n        const response = await fetch(graphqlEndpoint, {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(body),\n            credentials: \"include\"\n        });\n        console.log(\"\\uD83D\\uDD04 GraphQL Proxy - Response status:\", response.status);\n        console.log(\"\\uD83D\\uDD04 GraphQL Proxy - Response headers:\", Object.fromEntries(response.headers.entries()));\n        if (!response.ok) {\n            console.error(\"❌ GraphQL Proxy - Response not OK:\", response.status, response.statusText);\n            // Try to get response text for debugging\n            const errorText = await response.text();\n            console.error(\"❌ GraphQL Proxy - Error response body:\", errorText);\n            throw new Error(`GraphQL request failed: ${response.status} ${response.statusText}`);\n        }\n        // Check content type before parsing JSON\n        const contentType = response.headers.get(\"content-type\");\n        console.log(\"\\uD83D\\uDCC4 GraphQL Proxy - Content-Type:\", contentType);\n        if (!contentType || !contentType.includes(\"application/json\")) {\n            const responseText = await response.text();\n            console.error(\"❌ GraphQL Proxy - Non-JSON response received:\", responseText.substring(0, 500));\n            throw new Error(`Expected JSON response but received: ${contentType}. Response: ${responseText.substring(0, 200)}`);\n        }\n        // Get the response data\n        const data = await response.json();\n        console.log(\"✅ GraphQL Proxy - Response data:\", JSON.stringify(data, null, 2));\n        // Prepare response headers - use actual origin instead of wildcard for credentials support\n        const responseHeaders = {\n            \"Access-Control-Allow-Origin\": origin,\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session, cookie\",\n            \"Access-Control-Allow-Credentials\": \"true\",\n            \"Vary\": \"Origin\"\n        };\n        // Forward session token from WooCommerce response if present\n        const responseSessionHeader = response.headers.get(\"woocommerce-session\");\n        if (responseSessionHeader) {\n            responseHeaders[\"woocommerce-session\"] = responseSessionHeader;\n        }\n        // Forward any cookies from the WordPress response\n        const setCookieHeader = response.headers.get(\"set-cookie\");\n        if (setCookieHeader) {\n            responseHeaders[\"set-cookie\"] = setCookieHeader;\n        }\n        // Return the response with CORS headers\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status,\n            headers: responseHeaders\n        });\n    } catch (error) {\n        console.error(\"GraphQL proxy error:\", error);\n        // Get the origin for error response\n        const origin = request.headers.get(\"origin\") || \"\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            errors: [\n                {\n                    message: error instanceof Error ? error.message : \"Unknown error occurred\"\n                }\n            ]\n        }, {\n            status: 500,\n            headers: {\n                \"Access-Control-Allow-Origin\": origin,\n                \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session, cookie\",\n                \"Access-Control-Allow-Credentials\": \"true\",\n                \"Vary\": \"Origin\"\n            }\n        });\n    }\n}\n/**\n * Handle OPTIONS requests for CORS preflight\n */ async function OPTIONS(request) {\n    // Get the origin for CORS\n    const origin = request.headers.get(\"origin\") || \"\";\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 204,\n        headers: {\n            \"Access-Control-Allow-Origin\": origin,\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session, cookie\",\n            \"Access-Control-Allow-Credentials\": \"true\",\n            \"Access-Control-Max-Age\": \"86400\",\n            \"Vary\": \"Origin\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/graphql/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();