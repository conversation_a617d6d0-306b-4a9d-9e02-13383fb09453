"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_com"],{

/***/ "(app-pages-browser)/./src/components/ui/loader.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/loader.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Loader = (param)=>{\n    let { size = \"md\", color = \"#2c2c27\", className = \"\" } = param;\n    // Size mappings\n    const sizeMap = {\n        sm: {\n            container: \"w-6 h-6\",\n            dot: \"w-1 h-1\"\n        },\n        md: {\n            container: \"w-10 h-10\",\n            dot: \"w-1.5 h-1.5\"\n        },\n        lg: {\n            container: \"w-16 h-16\",\n            dot: \"w-2 h-2\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                children: \"\\n        @keyframes loaderRotate {\\n          0% {\\n            transform: rotate(0deg);\\n          }\\n          100% {\\n            transform: rotate(360deg);\\n          }\\n        }\\n        \\n        @keyframes loaderDot1 {\\n          0%, 100% {\\n            opacity: 0.2;\\n          }\\n          25% {\\n            opacity: 1;\\n          }\\n        }\\n        \\n        @keyframes loaderDot2 {\\n          0%, 100% {\\n            opacity: 0.2;\\n          }\\n          50% {\\n            opacity: 1;\\n          }\\n        }\\n        \\n        @keyframes loaderDot3 {\\n          0%, 100% {\\n            opacity: 0.2;\\n          }\\n          75% {\\n            opacity: 1;\\n          }\\n        }\\n        \\n        @keyframes loaderDot4 {\\n          0%, 100% {\\n            opacity: 1;\\n          }\\n          50% {\\n            opacity: 0.2;\\n          }\\n        }\\n      \"\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center \".concat(className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative \".concat(sizeMap[size].container),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-1/2 -translate-x-1/2 \".concat(sizeMap[size].dot, \" rounded-full\"),\n                            style: {\n                                backgroundColor: color,\n                                animation: \"loaderDot1 1.5s infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 right-0 -translate-y-1/2 \".concat(sizeMap[size].dot, \" rounded-full\"),\n                            style: {\n                                backgroundColor: color,\n                                animation: \"loaderDot2 1.5s infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-1/2 -translate-x-1/2 \".concat(sizeMap[size].dot, \" rounded-full\"),\n                            style: {\n                                backgroundColor: color,\n                                animation: \"loaderDot3 1.5s infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 left-0 -translate-y-1/2 \".concat(sizeMap[size].dot, \" rounded-full\"),\n                            style: {\n                                backgroundColor: color,\n                                animation: \"loaderDot4 1.5s infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 rounded-full\",\n                            style: {\n                                border: \"2px solid \".concat(color),\n                                borderTopColor: \"transparent\",\n                                animation: \"loaderRotate 1s linear infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c = Loader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Loader);\nvar _c;\n$RefreshReg$(_c, \"Loader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/loader.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/eventBus */ \"(app-pages-browser)/./src/lib/eventBus.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Create context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Auth provider component\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize auth state from cookies/localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, []);\n    const initializeAuth = async ()=>{\n        // Skip auth initialization during build/SSR\n        if (false) {}\n        setIsLoading(true);\n        try {\n            // Check if user is already authenticated\n            const response = await fetch(\"/api/auth/me\", {\n                credentials: \"include\"\n            });\n            console.log(\"Auth initialization response:\", response.status, response.ok);\n            if (response.ok) {\n                const data = await response.json();\n                console.log(\"Auth initialization data:\", data);\n                if (data.success && data.user) {\n                    setUser(data.user);\n                    setToken(data.token || \"authenticated\"); // Fallback for cookie-based auth\n                    console.log(\"User authenticated:\", data.user.email);\n                } else {\n                    console.log(\"Auth initialization failed:\", data.message);\n                }\n            } else if (response.status === 401) {\n                // Token might be expired - try to refresh\n                console.log(\"Auth initialization failed with 401, attempting token refresh\");\n                await refreshSession();\n            } else {\n                console.log(\"Auth initialization response not ok:\", response.status);\n            }\n        } catch (error) {\n            console.error(\"Failed to initialize auth:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/auth\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"login\",\n                    username: email,\n                    password\n                }),\n                credentials: \"include\"\n            });\n            const result = await response.json();\n            if (result.success) {\n                setUser(result.user);\n                setToken(result.token || \"authenticated\");\n                // Emit success event for other components\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.loginSuccess(result.user, result.token || \"authenticated\");\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(\"Login successful!\", \"success\");\n            } else {\n                const errorMessage = result.message || \"Login failed\";\n                setError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.loginError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n                throw new Error(errorMessage);\n            }\n        } catch (error) {\n            const errorMessage = error.message || \"Login failed\";\n            setError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.loginError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/auth\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"register\",\n                    ...userData\n                }),\n                credentials: \"include\"\n            });\n            const result = await response.json();\n            if (result.success) {\n                setUser(result.user);\n                setToken(result.token || \"authenticated\");\n                // Emit success event for other components\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.registerSuccess(result.user, result.token || \"authenticated\");\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(\"Registration successful!\", \"success\");\n            } else {\n                const errorMessage = result.message || \"Registration failed\";\n                setError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.registerError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n                throw new Error(errorMessage);\n            }\n        } catch (error) {\n            const errorMessage = error.message || \"Registration failed\";\n            setError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.registerError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        setIsLoading(true);\n        try {\n            await fetch(\"/api/auth\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"logout\"\n                }),\n                credentials: \"include\"\n            });\n        } catch (error) {\n            console.error(\"Logout API call failed:\", error);\n        }\n        // Clear state regardless of API call result\n        setUser(null);\n        setToken(null);\n        setError(null);\n        // Emit logout event for other components\n        _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.logout();\n        _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(\"Logged out successfully\", \"info\");\n        setIsLoading(false);\n    };\n    const refreshSession = async ()=>{\n        // Skip refresh during build/SSR\n        if (false) {}\n        try {\n            // First try to refresh the token if it's expired\n            const refreshResponse = await fetch(\"/api/auth\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"refresh\"\n                }),\n                credentials: \"include\"\n            });\n            // If refresh was successful, try to get user data\n            if (refreshResponse.ok) {\n                const refreshData = await refreshResponse.json();\n                if (refreshData.success) {\n                    console.log(\"Token refreshed successfully in AuthContext\");\n                }\n            }\n            // Now try to get the current user (whether refresh worked or not)\n            const response = await fetch(\"/api/auth/me\", {\n                credentials: \"include\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.user) {\n                    setUser(data.user);\n                    setToken(data.token || \"authenticated\");\n                    console.log(\"Session refreshed successfully for user:\", data.user.email);\n                    return;\n                }\n            }\n            // If we get here, session is invalid\n            console.log(\"Session refresh failed, clearing state\");\n            setUser(null);\n            setToken(null);\n        } catch (error) {\n            console.error(\"Failed to refresh session:\", error);\n            setUser(null);\n            setToken(null);\n        }\n    };\n    const updateProfile = async (data)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/auth/update-profile\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data),\n                credentials: \"include\"\n            });\n            const result = await response.json();\n            if (result.success) {\n                setUser(result.user);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.profileUpdated(result.user);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(\"Profile updated successfully!\", \"success\");\n                return result.user;\n            } else {\n                const errorMessage = result.message || \"Profile update failed\";\n                setError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n                throw new Error(errorMessage);\n            }\n        } catch (error) {\n            const errorMessage = error.message || \"Profile update failed\";\n            setError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const clearError = ()=>{\n        setError(null);\n    };\n    const isAuthenticated = !!user && !!token;\n    const value = {\n        user,\n        token,\n        isAuthenticated,\n        isLoading,\n        error,\n        login,\n        register,\n        logout,\n        refreshSession,\n        updateProfile,\n        clearError\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"mzZbxlz3rJTcku+Jn+vbpbhpSMU=\");\n_c = AuthProvider;\n// Hook to use auth context\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFeUY7QUFDekI7QUF5QmhFLGlCQUFpQjtBQUNqQixNQUFNTyw0QkFBY04sb0RBQWFBLENBQStCTztBQUVoRSwwQkFBMEI7QUFDbkIsU0FBU0MsYUFBYSxLQUFxQztRQUFyQyxFQUFFQyxRQUFRLEVBQTJCLEdBQXJDOztJQUMzQixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR1QsK0NBQVFBLENBQWE7SUFDN0MsTUFBTSxDQUFDVSxPQUFPQyxTQUFTLEdBQUdYLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNZLFdBQVdDLGFBQWEsR0FBR2IsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDYyxPQUFPQyxTQUFTLEdBQUdmLCtDQUFRQSxDQUFnQjtJQUVsRCxrREFBa0Q7SUFDbERDLGdEQUFTQSxDQUFDO1FBQ1JlO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUEsaUJBQWlCO1FBQ3JCLDRDQUE0QztRQUM1QyxJQUFJLEtBQWtCLEVBQWEsRUFHbEM7UUFFREgsYUFBYTtRQUNiLElBQUk7WUFDRix5Q0FBeUM7WUFDekMsTUFBTUksV0FBVyxNQUFNQyxNQUFNLGdCQUFnQjtnQkFDM0NDLGFBQWE7WUFDZjtZQUVBQyxRQUFRQyxHQUFHLENBQUMsaUNBQWlDSixTQUFTSyxNQUFNLEVBQUVMLFNBQVNNLEVBQUU7WUFFekUsSUFBSU4sU0FBU00sRUFBRSxFQUFFO2dCQUNmLE1BQU1DLE9BQU8sTUFBTVAsU0FBU1EsSUFBSTtnQkFDaENMLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJHO2dCQUN6QyxJQUFJQSxLQUFLRSxPQUFPLElBQUlGLEtBQUtoQixJQUFJLEVBQUU7b0JBQzdCQyxRQUFRZSxLQUFLaEIsSUFBSTtvQkFDakJHLFNBQVNhLEtBQUtkLEtBQUssSUFBSSxrQkFBa0IsaUNBQWlDO29CQUMxRVUsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QkcsS0FBS2hCLElBQUksQ0FBQ21CLEtBQUs7Z0JBQ3BELE9BQU87b0JBQ0xQLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JHLEtBQUtJLE9BQU87Z0JBQ3pEO1lBQ0YsT0FBTyxJQUFJWCxTQUFTSyxNQUFNLEtBQUssS0FBSztnQkFDbEMsMENBQTBDO2dCQUMxQ0YsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE1BQU1RO1lBQ1IsT0FBTztnQkFDTFQsUUFBUUMsR0FBRyxDQUFDLHdDQUF3Q0osU0FBU0ssTUFBTTtZQUNyRTtRQUNGLEVBQUUsT0FBT1IsT0FBTztZQUNkTSxRQUFRTixLQUFLLENBQUMsOEJBQThCQTtRQUM5QyxTQUFVO1lBQ1JELGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTWlCLFFBQVEsT0FBT0gsT0FBZUk7UUFDbENsQixhQUFhO1FBQ2JFLFNBQVM7UUFFVCxJQUFJO1lBQ0YsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLGFBQWE7Z0JBQ3hDYyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLFFBQVE7b0JBQ1JDLFVBQVVYO29CQUNWSTtnQkFDRjtnQkFDQVosYUFBYTtZQUNmO1lBRUEsTUFBTW9CLFNBQVMsTUFBTXRCLFNBQVNRLElBQUk7WUFFbEMsSUFBSWMsT0FBT2IsT0FBTyxFQUFFO2dCQUNsQmpCLFFBQVE4QixPQUFPL0IsSUFBSTtnQkFDbkJHLFNBQVM0QixPQUFPN0IsS0FBSyxJQUFJO2dCQUV6QiwwQ0FBMEM7Z0JBQzFDUixxREFBVUEsQ0FBQ3NDLFlBQVksQ0FBQ0QsT0FBTy9CLElBQUksRUFBRStCLE9BQU83QixLQUFLLElBQUk7Z0JBQ3JEUCw2REFBa0JBLENBQUNzQyxJQUFJLENBQUMscUJBQXFCO1lBQy9DLE9BQU87Z0JBQ0wsTUFBTUMsZUFBZUgsT0FBT1gsT0FBTyxJQUFJO2dCQUN2Q2IsU0FBUzJCO2dCQUNUeEMscURBQVVBLENBQUN5QyxVQUFVLENBQUNEO2dCQUN0QnZDLDZEQUFrQkEsQ0FBQ3NDLElBQUksQ0FBQ0MsY0FBYztnQkFDdEMsTUFBTSxJQUFJRSxNQUFNRjtZQUNsQjtRQUNGLEVBQUUsT0FBTzVCLE9BQVk7WUFDbkIsTUFBTTRCLGVBQWU1QixNQUFNYyxPQUFPLElBQUk7WUFDdENiLFNBQVMyQjtZQUNUeEMscURBQVVBLENBQUN5QyxVQUFVLENBQUNEO1lBQ3RCdkMsNkRBQWtCQSxDQUFDc0MsSUFBSSxDQUFDQyxjQUFjO1lBQ3RDLE1BQU01QjtRQUNSLFNBQVU7WUFDUkQsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNZ0MsV0FBVyxPQUFPQztRQUN0QmpDLGFBQWE7UUFDYkUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNRSxXQUFXLE1BQU1DLE1BQU0sYUFBYTtnQkFDeENjLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQkMsUUFBUTtvQkFDUixHQUFHUyxRQUFRO2dCQUNiO2dCQUNBM0IsYUFBYTtZQUNmO1lBRUEsTUFBTW9CLFNBQVMsTUFBTXRCLFNBQVNRLElBQUk7WUFFbEMsSUFBSWMsT0FBT2IsT0FBTyxFQUFFO2dCQUNsQmpCLFFBQVE4QixPQUFPL0IsSUFBSTtnQkFDbkJHLFNBQVM0QixPQUFPN0IsS0FBSyxJQUFJO2dCQUV6QiwwQ0FBMEM7Z0JBQzFDUixxREFBVUEsQ0FBQzZDLGVBQWUsQ0FBQ1IsT0FBTy9CLElBQUksRUFBRStCLE9BQU83QixLQUFLLElBQUk7Z0JBQ3hEUCw2REFBa0JBLENBQUNzQyxJQUFJLENBQUMsNEJBQTRCO1lBQ3RELE9BQU87Z0JBQ0wsTUFBTUMsZUFBZUgsT0FBT1gsT0FBTyxJQUFJO2dCQUN2Q2IsU0FBUzJCO2dCQUNUeEMscURBQVVBLENBQUM4QyxhQUFhLENBQUNOO2dCQUN6QnZDLDZEQUFrQkEsQ0FBQ3NDLElBQUksQ0FBQ0MsY0FBYztnQkFDdEMsTUFBTSxJQUFJRSxNQUFNRjtZQUNsQjtRQUNGLEVBQUUsT0FBTzVCLE9BQVk7WUFDbkIsTUFBTTRCLGVBQWU1QixNQUFNYyxPQUFPLElBQUk7WUFDdENiLFNBQVMyQjtZQUNUeEMscURBQVVBLENBQUM4QyxhQUFhLENBQUNOO1lBQ3pCdkMsNkRBQWtCQSxDQUFDc0MsSUFBSSxDQUFDQyxjQUFjO1lBQ3RDLE1BQU01QjtRQUNSLFNBQVU7WUFDUkQsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNb0MsU0FBUztRQUNicEMsYUFBYTtRQUViLElBQUk7WUFDRixNQUFNSyxNQUFNLGFBQWE7Z0JBQ3ZCYyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLFFBQVE7Z0JBQ1Y7Z0JBQ0FsQixhQUFhO1lBQ2Y7UUFDRixFQUFFLE9BQU9MLE9BQU87WUFDZE0sUUFBUU4sS0FBSyxDQUFDLDJCQUEyQkE7UUFDM0M7UUFFQSw0Q0FBNEM7UUFDNUNMLFFBQVE7UUFDUkUsU0FBUztRQUNUSSxTQUFTO1FBRVQseUNBQXlDO1FBQ3pDYixxREFBVUEsQ0FBQytDLE1BQU07UUFDakI5Qyw2REFBa0JBLENBQUNzQyxJQUFJLENBQUMsMkJBQTJCO1FBRW5ENUIsYUFBYTtJQUNmO0lBRUEsTUFBTWdCLGlCQUFpQjtRQUNyQixnQ0FBZ0M7UUFDaEMsSUFBSSxLQUFrQixFQUFhLEVBRWxDO1FBRUQsSUFBSTtZQUNGLGlEQUFpRDtZQUNqRCxNQUFNcUIsa0JBQWtCLE1BQU1oQyxNQUFNLGFBQWE7Z0JBQy9DYyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLFFBQVE7Z0JBQ1Y7Z0JBQ0FsQixhQUFhO1lBQ2Y7WUFFQSxrREFBa0Q7WUFDbEQsSUFBSStCLGdCQUFnQjNCLEVBQUUsRUFBRTtnQkFDdEIsTUFBTTRCLGNBQWMsTUFBTUQsZ0JBQWdCekIsSUFBSTtnQkFDOUMsSUFBSTBCLFlBQVl6QixPQUFPLEVBQUU7b0JBQ3ZCTixRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7WUFDRjtZQUVBLGtFQUFrRTtZQUNsRSxNQUFNSixXQUFXLE1BQU1DLE1BQU0sZ0JBQWdCO2dCQUMzQ0MsYUFBYTtZQUNmO1lBRUEsSUFBSUYsU0FBU00sRUFBRSxFQUFFO2dCQUNmLE1BQU1DLE9BQU8sTUFBTVAsU0FBU1EsSUFBSTtnQkFDaEMsSUFBSUQsS0FBS0UsT0FBTyxJQUFJRixLQUFLaEIsSUFBSSxFQUFFO29CQUM3QkMsUUFBUWUsS0FBS2hCLElBQUk7b0JBQ2pCRyxTQUFTYSxLQUFLZCxLQUFLLElBQUk7b0JBQ3ZCVSxRQUFRQyxHQUFHLENBQUMsNENBQTRDRyxLQUFLaEIsSUFBSSxDQUFDbUIsS0FBSztvQkFDdkU7Z0JBQ0Y7WUFDRjtZQUVBLHFDQUFxQztZQUNyQ1AsUUFBUUMsR0FBRyxDQUFDO1lBQ1paLFFBQVE7WUFDUkUsU0FBUztRQUNYLEVBQUUsT0FBT0csT0FBTztZQUNkTSxRQUFRTixLQUFLLENBQUMsOEJBQThCQTtZQUM1Q0wsUUFBUTtZQUNSRSxTQUFTO1FBQ1g7SUFDRjtJQUVBLE1BQU15QyxnQkFBZ0IsT0FBTzVCO1FBQzNCWCxhQUFhO1FBQ2JFLFNBQVM7UUFFVCxJQUFJO1lBQ0YsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLDRCQUE0QjtnQkFDdkRjLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDWjtnQkFDckJMLGFBQWE7WUFDZjtZQUVBLE1BQU1vQixTQUFTLE1BQU10QixTQUFTUSxJQUFJO1lBRWxDLElBQUljLE9BQU9iLE9BQU8sRUFBRTtnQkFDbEJqQixRQUFROEIsT0FBTy9CLElBQUk7Z0JBQ25CTixxREFBVUEsQ0FBQ21ELGNBQWMsQ0FBQ2QsT0FBTy9CLElBQUk7Z0JBQ3JDTCw2REFBa0JBLENBQUNzQyxJQUFJLENBQUMsaUNBQWlDO2dCQUN6RCxPQUFPRixPQUFPL0IsSUFBSTtZQUNwQixPQUFPO2dCQUNMLE1BQU1rQyxlQUFlSCxPQUFPWCxPQUFPLElBQUk7Z0JBQ3ZDYixTQUFTMkI7Z0JBQ1R2Qyw2REFBa0JBLENBQUNzQyxJQUFJLENBQUNDLGNBQWM7Z0JBQ3RDLE1BQU0sSUFBSUUsTUFBTUY7WUFDbEI7UUFDRixFQUFFLE9BQU81QixPQUFZO1lBQ25CLE1BQU00QixlQUFlNUIsTUFBTWMsT0FBTyxJQUFJO1lBQ3RDYixTQUFTMkI7WUFDVHZDLDZEQUFrQkEsQ0FBQ3NDLElBQUksQ0FBQ0MsY0FBYztZQUN0QyxNQUFNNUI7UUFDUixTQUFVO1lBQ1JELGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTXlDLGFBQWE7UUFDakJ2QyxTQUFTO0lBQ1g7SUFFQSxNQUFNd0Msa0JBQWtCLENBQUMsQ0FBQy9DLFFBQVEsQ0FBQyxDQUFDRTtJQUVwQyxNQUFNOEMsUUFBMEI7UUFDOUJoRDtRQUNBRTtRQUNBNkM7UUFDQTNDO1FBQ0FFO1FBQ0FnQjtRQUNBZTtRQUNBSTtRQUNBcEI7UUFDQXVCO1FBQ0FFO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ2xELFlBQVlxRCxRQUFRO1FBQUNELE9BQU9BO2tCQUMxQmpEOzs7Ozs7QUFHUDtHQTdSZ0JEO0tBQUFBO0FBK1JoQiwyQkFBMkI7QUFDcEIsU0FBU29EOztJQUNkLE1BQU1DLFVBQVU1RCxpREFBVUEsQ0FBQ0s7SUFDM0IsSUFBSXVELFlBQVl0RCxXQUFXO1FBQ3pCLE1BQU0sSUFBSXVDLE1BQU07SUFDbEI7SUFDQSxPQUFPZTtBQUNUO0lBTmdCRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4PzFmYTIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlU3RhdGUsIHVzZUVmZmVjdCwgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgYXV0aEV2ZW50cywgbm90aWZpY2F0aW9uRXZlbnRzIH0gZnJvbSAnQC9saWIvZXZlbnRCdXMnO1xuXG4vLyBBdXRoIGNvbnRleHQgaW50ZXJmYWNlXG5leHBvcnQgaW50ZXJmYWNlIEF1dGhDb250ZXh0VmFsdWUge1xuICB1c2VyOiBhbnkgfCBudWxsO1xuICB0b2tlbjogc3RyaW5nIHwgbnVsbDtcbiAgaXNBdXRoZW50aWNhdGVkOiBib29sZWFuO1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuICBsb2dpbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IFByb21pc2U8dm9pZD47XG4gIHJlZ2lzdGVyOiAodXNlckRhdGE6IFJlZ2lzdGVyRGF0YSkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgbG9nb3V0OiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICByZWZyZXNoU2Vzc2lvbjogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgdXBkYXRlUHJvZmlsZTogKGRhdGE6IGFueSkgPT4gUHJvbWlzZTxhbnk+O1xuICBjbGVhckVycm9yOiAoKSA9PiB2b2lkO1xufVxuXG4vLyBSZWdpc3RyYXRpb24gZGF0YSBpbnRlcmZhY2VcbmV4cG9ydCBpbnRlcmZhY2UgUmVnaXN0ZXJEYXRhIHtcbiAgZW1haWw6IHN0cmluZztcbiAgZmlyc3ROYW1lOiBzdHJpbmc7XG4gIGxhc3ROYW1lOiBzdHJpbmc7XG4gIHBhc3N3b3JkOiBzdHJpbmc7XG59XG5cbi8vIENyZWF0ZSBjb250ZXh0XG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRWYWx1ZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcblxuLy8gQXV0aCBwcm92aWRlciBjb21wb25lbnRcbmV4cG9ydCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfSkge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxhbnkgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3Rva2VuLCBzZXRUb2tlbl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIC8vIEluaXRpYWxpemUgYXV0aCBzdGF0ZSBmcm9tIGNvb2tpZXMvbG9jYWxTdG9yYWdlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaW5pdGlhbGl6ZUF1dGgoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGluaXRpYWxpemVBdXRoID0gYXN5bmMgKCkgPT4ge1xuICAgIC8vIFNraXAgYXV0aCBpbml0aWFsaXphdGlvbiBkdXJpbmcgYnVpbGQvU1NSXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBhbHJlYWR5IGF1dGhlbnRpY2F0ZWRcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9tZScsIHtcbiAgICAgICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJyxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zb2xlLmxvZygnQXV0aCBpbml0aWFsaXphdGlvbiByZXNwb25zZTonLCByZXNwb25zZS5zdGF0dXMsIHJlc3BvbnNlLm9rKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIGluaXRpYWxpemF0aW9uIGRhdGE6JywgZGF0YSk7XG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MgJiYgZGF0YS51c2VyKSB7XG4gICAgICAgICAgc2V0VXNlcihkYXRhLnVzZXIpO1xuICAgICAgICAgIHNldFRva2VuKGRhdGEudG9rZW4gfHwgJ2F1dGhlbnRpY2F0ZWQnKTsgLy8gRmFsbGJhY2sgZm9yIGNvb2tpZS1iYXNlZCBhdXRoXG4gICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgYXV0aGVudGljYXRlZDonLCBkYXRhLnVzZXIuZW1haWwpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIGluaXRpYWxpemF0aW9uIGZhaWxlZDonLCBkYXRhLm1lc3NhZ2UpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7XG4gICAgICAgIC8vIFRva2VuIG1pZ2h0IGJlIGV4cGlyZWQgLSB0cnkgdG8gcmVmcmVzaFxuICAgICAgICBjb25zb2xlLmxvZygnQXV0aCBpbml0aWFsaXphdGlvbiBmYWlsZWQgd2l0aCA0MDEsIGF0dGVtcHRpbmcgdG9rZW4gcmVmcmVzaCcpO1xuICAgICAgICBhd2FpdCByZWZyZXNoU2Vzc2lvbigpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0F1dGggaW5pdGlhbGl6YXRpb24gcmVzcG9uc2Ugbm90IG9rOicsIHJlc3BvbnNlLnN0YXR1cyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBpbml0aWFsaXplIGF1dGg6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2dpbiA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgYWN0aW9uOiAnbG9naW4nLFxuICAgICAgICAgIHVzZXJuYW1lOiBlbWFpbCxcbiAgICAgICAgICBwYXNzd29yZCxcbiAgICAgICAgfSksXG4gICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0VXNlcihyZXN1bHQudXNlcik7XG4gICAgICAgIHNldFRva2VuKHJlc3VsdC50b2tlbiB8fCAnYXV0aGVudGljYXRlZCcpO1xuICAgICAgICBcbiAgICAgICAgLy8gRW1pdCBzdWNjZXNzIGV2ZW50IGZvciBvdGhlciBjb21wb25lbnRzXG4gICAgICAgIGF1dGhFdmVudHMubG9naW5TdWNjZXNzKHJlc3VsdC51c2VyLCByZXN1bHQudG9rZW4gfHwgJ2F1dGhlbnRpY2F0ZWQnKTtcbiAgICAgICAgbm90aWZpY2F0aW9uRXZlbnRzLnNob3coJ0xvZ2luIHN1Y2Nlc3NmdWwhJywgJ3N1Y2Nlc3MnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IHJlc3VsdC5tZXNzYWdlIHx8ICdMb2dpbiBmYWlsZWQnO1xuICAgICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgICAgICBhdXRoRXZlbnRzLmxvZ2luRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgICAgbm90aWZpY2F0aW9uRXZlbnRzLnNob3coZXJyb3JNZXNzYWdlLCAnZXJyb3InKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IubWVzc2FnZSB8fCAnTG9naW4gZmFpbGVkJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICBhdXRoRXZlbnRzLmxvZ2luRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIG5vdGlmaWNhdGlvbkV2ZW50cy5zaG93KGVycm9yTWVzc2FnZSwgJ2Vycm9yJyk7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVnaXN0ZXIgPSBhc3luYyAodXNlckRhdGE6IFJlZ2lzdGVyRGF0YSkgPT4ge1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIGFjdGlvbjogJ3JlZ2lzdGVyJyxcbiAgICAgICAgICAuLi51c2VyRGF0YSxcbiAgICAgICAgfSksXG4gICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0VXNlcihyZXN1bHQudXNlcik7XG4gICAgICAgIHNldFRva2VuKHJlc3VsdC50b2tlbiB8fCAnYXV0aGVudGljYXRlZCcpO1xuICAgICAgICBcbiAgICAgICAgLy8gRW1pdCBzdWNjZXNzIGV2ZW50IGZvciBvdGhlciBjb21wb25lbnRzXG4gICAgICAgIGF1dGhFdmVudHMucmVnaXN0ZXJTdWNjZXNzKHJlc3VsdC51c2VyLCByZXN1bHQudG9rZW4gfHwgJ2F1dGhlbnRpY2F0ZWQnKTtcbiAgICAgICAgbm90aWZpY2F0aW9uRXZlbnRzLnNob3coJ1JlZ2lzdHJhdGlvbiBzdWNjZXNzZnVsIScsICdzdWNjZXNzJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSByZXN1bHQubWVzc2FnZSB8fCAnUmVnaXN0cmF0aW9uIGZhaWxlZCc7XG4gICAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICAgIGF1dGhFdmVudHMucmVnaXN0ZXJFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgICAgICBub3RpZmljYXRpb25FdmVudHMuc2hvdyhlcnJvck1lc3NhZ2UsICdlcnJvcicpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlIHx8ICdSZWdpc3RyYXRpb24gZmFpbGVkJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICBhdXRoRXZlbnRzLnJlZ2lzdGVyRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIG5vdGlmaWNhdGlvbkV2ZW50cy5zaG93KGVycm9yTWVzc2FnZSwgJ2Vycm9yJyk7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgYXdhaXQgZmV0Y2goJy9hcGkvYXV0aCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgYWN0aW9uOiAnbG9nb3V0JyxcbiAgICAgICAgfSksXG4gICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTG9nb3V0IEFQSSBjYWxsIGZhaWxlZDonLCBlcnJvcik7XG4gICAgfVxuXG4gICAgLy8gQ2xlYXIgc3RhdGUgcmVnYXJkbGVzcyBvZiBBUEkgY2FsbCByZXN1bHRcbiAgICBzZXRVc2VyKG51bGwpO1xuICAgIHNldFRva2VuKG51bGwpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIFxuICAgIC8vIEVtaXQgbG9nb3V0IGV2ZW50IGZvciBvdGhlciBjb21wb25lbnRzXG4gICAgYXV0aEV2ZW50cy5sb2dvdXQoKTtcbiAgICBub3RpZmljYXRpb25FdmVudHMuc2hvdygnTG9nZ2VkIG91dCBzdWNjZXNzZnVsbHknLCAnaW5mbycpO1xuICAgIFxuICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgcmVmcmVzaFNlc3Npb24gPSBhc3luYyAoKSA9PiB7XG4gICAgLy8gU2tpcCByZWZyZXNoIGR1cmluZyBidWlsZC9TU1JcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gRmlyc3QgdHJ5IHRvIHJlZnJlc2ggdGhlIHRva2VuIGlmIGl0J3MgZXhwaXJlZFxuICAgICAgY29uc3QgcmVmcmVzaFJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgYWN0aW9uOiAncmVmcmVzaCcsXG4gICAgICAgIH0pLFxuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgfSk7XG5cbiAgICAgIC8vIElmIHJlZnJlc2ggd2FzIHN1Y2Nlc3NmdWwsIHRyeSB0byBnZXQgdXNlciBkYXRhXG4gICAgICBpZiAocmVmcmVzaFJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IHJlZnJlc2hEYXRhID0gYXdhaXQgcmVmcmVzaFJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgaWYgKHJlZnJlc2hEYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnVG9rZW4gcmVmcmVzaGVkIHN1Y2Nlc3NmdWxseSBpbiBBdXRoQ29udGV4dCcpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIE5vdyB0cnkgdG8gZ2V0IHRoZSBjdXJyZW50IHVzZXIgKHdoZXRoZXIgcmVmcmVzaCB3b3JrZWQgb3Igbm90KVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL21lJywge1xuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBpZiAoZGF0YS5zdWNjZXNzICYmIGRhdGEudXNlcikge1xuICAgICAgICAgIHNldFVzZXIoZGF0YS51c2VyKTtcbiAgICAgICAgICBzZXRUb2tlbihkYXRhLnRva2VuIHx8ICdhdXRoZW50aWNhdGVkJyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1Nlc3Npb24gcmVmcmVzaGVkIHN1Y2Nlc3NmdWxseSBmb3IgdXNlcjonLCBkYXRhLnVzZXIuZW1haWwpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBJZiB3ZSBnZXQgaGVyZSwgc2Vzc2lvbiBpcyBpbnZhbGlkXG4gICAgICBjb25zb2xlLmxvZygnU2Vzc2lvbiByZWZyZXNoIGZhaWxlZCwgY2xlYXJpbmcgc3RhdGUnKTtcbiAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICBzZXRUb2tlbihudWxsKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHJlZnJlc2ggc2Vzc2lvbjonLCBlcnJvcik7XG4gICAgICBzZXRVc2VyKG51bGwpO1xuICAgICAgc2V0VG9rZW4obnVsbCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZVByb2ZpbGUgPSBhc3luYyAoZGF0YTogYW55KSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC91cGRhdGUtcHJvZmlsZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkYXRhKSxcbiAgICAgICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJyxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRVc2VyKHJlc3VsdC51c2VyKTtcbiAgICAgICAgYXV0aEV2ZW50cy5wcm9maWxlVXBkYXRlZChyZXN1bHQudXNlcik7XG4gICAgICAgIG5vdGlmaWNhdGlvbkV2ZW50cy5zaG93KCdQcm9maWxlIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5IScsICdzdWNjZXNzJyk7XG4gICAgICAgIHJldHVybiByZXN1bHQudXNlcjtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IHJlc3VsdC5tZXNzYWdlIHx8ICdQcm9maWxlIHVwZGF0ZSBmYWlsZWQnO1xuICAgICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgICAgICBub3RpZmljYXRpb25FdmVudHMuc2hvdyhlcnJvck1lc3NhZ2UsICdlcnJvcicpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlIHx8ICdQcm9maWxlIHVwZGF0ZSBmYWlsZWQnO1xuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIG5vdGlmaWNhdGlvbkV2ZW50cy5zaG93KGVycm9yTWVzc2FnZSwgJ2Vycm9yJyk7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgY2xlYXJFcnJvciA9ICgpID0+IHtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgfTtcblxuICBjb25zdCBpc0F1dGhlbnRpY2F0ZWQgPSAhIXVzZXIgJiYgISF0b2tlbjtcblxuICBjb25zdCB2YWx1ZTogQXV0aENvbnRleHRWYWx1ZSA9IHtcbiAgICB1c2VyLFxuICAgIHRva2VuLFxuICAgIGlzQXV0aGVudGljYXRlZCxcbiAgICBpc0xvYWRpbmcsXG4gICAgZXJyb3IsXG4gICAgbG9naW4sXG4gICAgcmVnaXN0ZXIsXG4gICAgbG9nb3V0LFxuICAgIHJlZnJlc2hTZXNzaW9uLFxuICAgIHVwZGF0ZVByb2ZpbGUsXG4gICAgY2xlYXJFcnJvcixcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxBdXRoQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvQXV0aENvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59XG5cbi8vIEhvb2sgdG8gdXNlIGF1dGggY29udGV4dFxuZXhwb3J0IGZ1bmN0aW9uIHVzZUF1dGgoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImF1dGhFdmVudHMiLCJub3RpZmljYXRpb25FdmVudHMiLCJBdXRoQ29udGV4dCIsInVuZGVmaW5lZCIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJ0b2tlbiIsInNldFRva2VuIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImluaXRpYWxpemVBdXRoIiwicmVzcG9uc2UiLCJmZXRjaCIsImNyZWRlbnRpYWxzIiwiY29uc29sZSIsImxvZyIsInN0YXR1cyIsIm9rIiwiZGF0YSIsImpzb24iLCJzdWNjZXNzIiwiZW1haWwiLCJtZXNzYWdlIiwicmVmcmVzaFNlc3Npb24iLCJsb2dpbiIsInBhc3N3b3JkIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiYWN0aW9uIiwidXNlcm5hbWUiLCJyZXN1bHQiLCJsb2dpblN1Y2Nlc3MiLCJzaG93IiwiZXJyb3JNZXNzYWdlIiwibG9naW5FcnJvciIsIkVycm9yIiwicmVnaXN0ZXIiLCJ1c2VyRGF0YSIsInJlZ2lzdGVyU3VjY2VzcyIsInJlZ2lzdGVyRXJyb3IiLCJsb2dvdXQiLCJyZWZyZXNoUmVzcG9uc2UiLCJyZWZyZXNoRGF0YSIsInVwZGF0ZVByb2ZpbGUiLCJwcm9maWxlVXBkYXRlZCIsImNsZWFyRXJyb3IiLCJpc0F1dGhlbnRpY2F0ZWQiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwidXNlQXV0aCIsImNvbnRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

}]);